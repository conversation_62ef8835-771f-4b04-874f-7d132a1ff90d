# SubscriptionFlow Webhook Processing Delay Configuration

## Overview
The SubscriptionFlow webhook API now supports configurable processing delays to help with rate limiting, data consistency, and avoiding "due" status issues when processing invoice details.

## Configuration

### Setting the Delay
Add the following setting to your Django settings file (e.g., `settings.py` or environment-specific settings):

```python
# SubscriptionFlow webhook processing delay in seconds
# Default: 0.3 seconds (300 milliseconds)
# Range: 0.0 to 4.0 seconds (recommended)
SUBSCRIPTIONFLOW_PROCESSING_DELAY = 0.3
```

### Recommended Values
- **0.3 seconds (default for subscriptions)**: Good balance between speed and reliability for subscription processing
- **4.0 seconds (for invoice processing)**: Added delay before fetching invoice details to ensure data consistency
- **0.0 seconds**: Disables delay (not recommended for production)

## What Gets Delayed

The delay is applied to the following webhook processing operations:

1. **Invoice Processing** (`handle_invoice_event`)
   - Applied before fetching complete invoice data from SubscriptionFlow API (hardcoded to 4 seconds)
   - Helps prevent race conditions with invoice status updates
   - Ensures invoice data is fully processed before fetching from API

2. **Transaction Processing** (`fetch_and_save_transaction`)
   - Applied before fetching transaction details
   - Ensures invoice is fully processed before related transactions
   - Uses default delay setting or 0.2 seconds if not configured

3. **Subscription Processing** (`fetch_and_save_subscription`)
   - Applied before fetching subscription details
   - Prevents conflicts when multiple webhooks reference the same subscription
   - Uses SUBSCRIPTIONFLOW_PROCESSING_DELAY setting (default: 0.3 seconds)

4. **Customer Processing** (`fetch_and_save_customer`)
   - Applied before fetching customer details
   - Ensures proper sequencing of customer data updates
   - Uses default delay setting or 0.2 seconds if not configured

## Benefits

### Prevents "Due" Status Issues
- Allows SubscriptionFlow's internal systems to fully process status changes
- Reduces race conditions between webhook delivery and API data availability
- Ensures consistent data when fetching complete details via API

### Rate Limiting Protection
- Spreads out API requests to avoid hitting rate limits
- Provides configurable backoff mechanism for high-volume environments