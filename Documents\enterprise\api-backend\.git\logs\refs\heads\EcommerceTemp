0000000000000000000000000000000000000000 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d <PERSON><PERSON><PERSON> <<EMAIL>> 1752582117 +0530	branch: Created from HEAD
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d e27ee115eec7e7492acb5ab0cd7a958ca0492d3a <PERSON><PERSON><PERSON> <<EMAIL>> 1752582252 +0530	commit: till fetch list of category done
e27ee115eec7e7492acb5ab0cd7a958ca0492d3a 42fcd355ed024b171432c6bc24a6ad3722a6db05 <PERSON><PERSON><PERSON> <<EMAIL>> 1752654137 +0530	commit: get category by id done
42fcd355ed024b171432c6bc24a6ad3722a6db05 567f882d73e7ffb8167179f99972f9b41bc63ac<PERSON> <PERSON><PERSON> <<EMAIL>> 1753447601 +0530	commit: course models update for ecommerce
567f882d73e7ffb8167179f99972f9b41bc63ac5 9493abbcd314cb5ef66cefb043ca9161ea158f60 Rahul Kumar <<EMAIL>> 1753724192 +0530	pull origin staging: Fast-forward
9493abbcd314cb5ef66cefb043ca9161ea158f60 d0ba07d4b20a8aa66d19aa29377416951ade89cb Rahul Kumar <<EMAIL>> 1753724544 +0530	pull origin staging: Fast-forward
d0ba07d4b20a8aa66d19aa29377416951ade89cb 5fc56cf47d05292990c80f6348998c1b6288d8ef Rahul Kumar <<EMAIL>> 1753773795 +0530	pull origin staging: Fast-forward
5fc56cf47d05292990c80f6348998c1b6288d8ef 5e1e7579375400742137c3d0d60715d23684f46a Rahul Kumar <<EMAIL>> 1755085452 +0530	commit: inintial setup and API created
5e1e7579375400742137c3d0d60715d23684f46a 54b498986198999dbc819a1251adf57a956e32e3 Rahul Kumar <<EMAIL>> 1755602777 +0530	pull origin staging: Fast-forward
54b498986198999dbc819a1251adf57a956e32e3 73a8f49c57a22c4313b23e45c0a476a44d75d51f Rahul Kumar <<EMAIL>> 1755602841 +0530	commit: db synced & celery part done
73a8f49c57a22c4313b23e45c0a476a44d75d51f 667ed7b94534753a4f833df7e54ef0abcd69ff02 Rahul Kumar <<EMAIL>> 1755755221 +0530	commit: Add search functionality to CourseDropdownsAndListAPIView
667ed7b94534753a4f833df7e54ef0abcd69ff02 24a45009cda77c1c2c39c557a41c08cab8e9ec26 Rahul Kumar <<EMAIL>> 1755759530 +0530	pull origin staging: Fast-forward
24a45009cda77c1c2c39c557a41c08cab8e9ec26 00193cff99a09168159bc8f07aba683e02f3201c Rahul Kumar <<EMAIL>> 1755858024 +0530	commit: update course detailed response
00193cff99a09168159bc8f07aba683e02f3201c 03ce88eaec30adc3a958a1631d993b7505fe453d Rahul Kumar <<EMAIL>> 1755865833 +0530	commit: updated course detailed responses
03ce88eaec30adc3a958a1631d993b7505fe453d 9b87415b96a9212fbb1ad4b5bb4b765f934153ce Rahul Kumar <<EMAIL>> 1756102250 +0530	commit: updated course detailed responses...
9b87415b96a9212fbb1ad4b5bb4b765f934153ce 3e4917fb2503172a4ea54aaa14af06ef312b8633 Rahul Kumar <<EMAIL>> 1756106889 +0530	commit: updated course detailed repsonse remove , add fixed pattern
3e4917fb2503172a4ea54aaa14af06ef312b8633 bb30cb571ac0f231be96d4a6f0e25d106141f4d1 Rahul Kumar <<EMAIL>> 1756108560 +0530	pull origin staging: Fast-forward
bb30cb571ac0f231be96d4a6f0e25d106141f4d1 9bb190a327378e9a3e22223d3a9041ff3a2158ea Rahul Kumar <<EMAIL>> 1756183833 +0530	commit: updated course detailed repsonse select top3
9bb190a327378e9a3e22223d3a9041ff3a2158ea 86d2a7755b70a4e3a89a510105207452d35cdab7 Rahul Kumar <<EMAIL>> 1756294062 +0530	commit: create SubscriptionFlowWebhook api
86d2a7755b70a4e3a89a510105207452d35cdab7 3bff5e73481f8ebda2697043e8fa416d3da3042b Rahul Kumar <<EMAIL>> 1756298636 +0530	commit: update bulkupload API
3bff5e73481f8ebda2697043e8fa416d3da3042b 1e73d4ab2f0dfe2f958d178ccfd8a7fd8fb3e4db Rahul Kumar <<EMAIL>> 1756314563 +0530	pull origin staging: Fast-forward
1e73d4ab2f0dfe2f958d178ccfd8a7fd8fb3e4db ba0b17010ef501d86c5edfc36adf9f32966a8190 Rahul Kumar <<EMAIL>> 1756359564 +0530	commit: update wehook api payload
ba0b17010ef501d86c5edfc36adf9f32966a8190 703a0a33f6ad10d0113875ed3df68aeb024f0789 Rahul Kumar <<EMAIL>> 1756446242 +0530	commit: Add configurable processing delays for SubscriptionFlow webhook handling
